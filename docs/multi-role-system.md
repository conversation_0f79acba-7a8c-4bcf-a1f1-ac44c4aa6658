# 多角色系统设计文档

## 概述

本文档描述了资管维平台的多角色系统重新设计，支持一个 openid 同时拥有多个角色，并提供灵活的角色切换和权限管理功能。

## 系统架构

### 数据库设计

#### 1. 用户表 (users)
- 移除了单一的 `role` 字段
- 添加了 `current_role_id` 字段来记录当前激活的角色
- 添加了 `primary_department` 字段来记录主要部门

#### 2. 用户角色关联表 (user_roles)
- 支持一个用户拥有多个角色
- 添加了 `priority` 字段来设置角色优先级
- 添加了 `role_context` 字段来存储角色上下文数据
- 添加了使用统计字段 (`last_used_at`, `usage_count`)
- 支持角色过期时间设置

#### 3. 角色权限定义表 (role_permissions)
- 定义了每个角色的默认权限
- 支持细粒度的权限控制
- 权限格式：`resource.action`（如 `equipment.view_own`）

#### 4. 用户会话表 (user_sessions)
- 记录用户的登录会话和当前角色上下文
- 支持会话管理和角色切换历史

### 后端实现

#### 1. 模型更新
- **User 模型**：移除单一角色字段，添加当前角色关联
- **UserRole 模型**：增强多角色支持，添加新的查询方法
- **RolePermission 模型**：新增权限管理模型
- **UserSession 模型**：新增会话管理模型

#### 2. 认证中间件
- 支持多角色用户的认证
- 自动获取用户的所有角色和当前激活角色
- 提供角色权限信息给请求上下文

#### 3. 角色控制器
- 提供角色切换 API
- 支持角色管理功能（添加、移除、设置默认角色）
- 提供权限检查接口

### 前端实现

#### 1. 角色选择页面
- 显示用户的所有角色
- 支持角色切换
- 显示角色优先级和默认角色标识

#### 2. 角色切换组件
- 提供快速角色切换功能
- 支持紧凑和完整两种显示模式
- 实时更新角色状态

#### 3. 权限管理工具
- 提供前端权限检查功能
- 支持基于角色的界面控制
- 提供权限相关的工具方法

## 角色类型和权限

### 角色类型
1. **user** - 普通用户
2. **pi** - PI负责人
3. **engineer** - 工程师
4. **admin** - 管理员
5. **super_admin** - 超级管理员

### 权限体系
权限采用 `resource.action` 的格式，例如：
- `equipment.view_own` - 查看自己的设备
- `equipment.view_group` - 查看课题组设备
- `equipment.view_all` - 查看所有设备
- `order.create` - 创建工单
- `order.update_assigned` - 更新分配给自己的工单
- `user.manage_roles` - 管理用户角色

## 使用方法

### 1. 角色切换

#### 前端调用
```javascript
// 使用角色ID切换
await app.switchRole(null, roleId);

// 使用角色类型切换
await app.switchRole('engineer');

// 使用新的API
const result = await app.request({
  url: '/roles/switch',
  method: 'POST',
  data: { targetRoleId: roleId }
});
```

#### 后端API
```
POST /api/roles/switch
{
  "targetRoleId": 123,  // 或者使用 targetRole: "engineer"
}
```

### 2. 权限检查

#### 前端权限检查
```javascript
const permission = require('../../utils/permission');

// 检查权限
if (permission.hasPermission('equipment.create')) {
  // 显示创建设备按钮
}

// 检查角色
if (permission.hasRole('admin')) {
  // 显示管理员功能
}

// 检查设备权限
if (permission.canAccessEquipment('update', equipment)) {
  // 允许编辑设备
}
```

#### 后端权限检查
```javascript
const { RolePermission } = require('../models');

// 检查用户权限
const hasPermission = await RolePermission.hasPermission(
  userRoles, 
  'equipment.create'
);
```

### 3. 角色管理

#### 添加角色
```javascript
// 管理员为用户添加角色
const result = await app.request({
  url: `/roles/users/${userId}/roles`,
  method: 'POST',
  data: {
    role: 'engineer',
    roleName: '维修工程师',
    department: '设备维修部',
    permissions: []
  }
});
```

#### 设置默认角色
```javascript
const result = await app.request({
  url: '/roles/set-default',
  method: 'POST',
  data: { role: 'pi' }
});
```

## 数据迁移

### 从单角色到多角色的迁移步骤

1. **备份现有数据**
2. **执行数据库结构更新**
   ```sql
   -- 执行 backend/database/init-cloud.sql 中的更新
   ```
3. **迁移现有用户角色**
   ```sql
   -- 将现有用户的角色迁移到 user_roles 表
   INSERT INTO user_roles (user_id, role, role_name, is_default, is_active, granted_at)
   SELECT id, role, 
          CASE role 
            WHEN 'user' THEN '普通用户'
            WHEN 'engineer' THEN '工程师'
            WHEN 'admin' THEN '管理员'
            WHEN 'super_admin' THEN '超级管理员'
          END,
          1, 1, NOW()
   FROM users 
   WHERE role IS NOT NULL;
   ```
4. **更新用户表的当前角色ID**
   ```sql
   UPDATE users u 
   SET current_role_id = (
     SELECT ur.id FROM user_roles ur 
     WHERE ur.user_id = u.id AND ur.is_default = 1 
     LIMIT 1
   );
   ```

## 测试

### 测试页面
创建了专门的测试页面 `/pages/role-test/role-test` 来验证多角色系统：
- 显示用户的所有角色
- 测试权限检查功能
- 验证角色切换功能
- 显示系统权限列表

### 测试用例
1. **单角色用户测试**
2. **多角色用户测试**
3. **角色切换测试**
4. **权限检查测试**
5. **角色过期测试**

## 注意事项

1. **向后兼容性**：保留了原有的角色切换方法，确保现有代码不受影响
2. **性能优化**：使用角色优先级和缓存机制提高性能
3. **安全性**：所有角色切换都需要验证用户是否真正拥有目标角色
4. **用户体验**：提供直观的角色选择界面和快速切换功能

## 未来扩展

1. **角色继承**：支持角色之间的继承关系
2. **动态权限**：支持运行时动态分配权限
3. **角色模板**：提供预定义的角色模板
4. **审计日志**：记录角色切换和权限使用历史
5. **批量角色管理**：支持批量为用户分配角色
