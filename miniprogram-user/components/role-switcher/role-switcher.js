// components/role-switcher/role-switcher.js
const app = getApp();

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 显示模式：compact(紧凑) | full(完整)
    mode: {
      type: String,
      value: 'full'
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentRole: '',
    currentRoleName: '',
    currentRoleIcon: '',
    availableRoles: [],
    showRoleList: false,
    loading: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 初始化角色信息
    initRoleInfo() {
      const currentRole = app.globalData.currentRole || 'user';
      const userInfo = app.globalData.userInfo || {};

      this.setData({
        currentRole,
        currentRoleName: this.getRoleName(currentRole),
        currentRoleIcon: this.getRoleIcon(currentRole)
      });

      this.loadAvailableRoles();
    },

    // 加载可用角色列表
    async loadAvailableRoles() {
      try {
        const result = await app.request({
          url: '/roles/my-roles',
          method: 'GET'
        });

        if (result.success) {
          const { roles } = result.data;
          const roleList = roles.filter(role => role.isActive).map(role => ({
            role: role.role,
            name: role.roleName,
            department: role.department,
            icon: this.getRoleIcon(role.role)
          }));

          this.setData({
            availableRoles: roleList
          });
        }
      } catch (error) {
        console.error('加载角色列表失败:', error);
      }
    },

    // 显示角色选择列表
    showRoleSelector() {
      if (this.data.availableRoles.length <= 1) {
        wx.showToast({
          title: '暂无其他角色',
          icon: 'none'
        });
        return;
      }
      
      this.setData({
        showRoleList: true
      });
    },

    // 隐藏角色选择列表
    hideRoleSelector() {
      this.setData({
        showRoleList: false
      });
    },

    // 选择角色
    async selectRole(e) {
      const { role, roleid } = e.currentTarget.dataset;

      if (role === this.data.currentRole) {
        this.hideRoleSelector();
        return;
      }

      if (this.data.loading) return;

      this.setData({ loading: true });

      try {
        // 使用角色ID进行切换，如果没有则使用角色类型
        const switchData = roleid ?
          { targetRoleId: parseInt(roleid) } :
          { targetRole: role };

        const result = await app.request({
          url: '/roles/switch',
          method: 'POST',
          data: switchData
        });

        if (result.success) {
          const { currentRole, roleId, roleName, token } = result.data;

          // 更新全局数据
          app.globalData.currentRole = currentRole;
          app.globalData.currentRoleId = roleId;
          app.globalData.currentRoleName = roleName;
          if (token) {
            app.globalData.token = token;
            wx.setStorageSync('token', token);
          }

          // 更新用户信息
          if (app.globalData.userInfo) {
            app.globalData.userInfo.currentRole = currentRole;
            app.globalData.userInfo.currentRoleId = roleId;
            wx.setStorageSync('userInfo', app.globalData.userInfo);
          }

          const newRoleName = roleName || this.getRoleName(currentRole);
          const newRoleIcon = this.getRoleIcon(currentRole);

          this.setData({
            currentRole: currentRole,
            currentRoleName: newRoleName,
            currentRoleIcon: newRoleIcon,
            showRoleList: false,
            loading: false
          });

          // 触发角色切换事件
          this.triggerEvent('rolechange', {
            newRole: currentRole,
            newRoleId: roleId,
            newRoleName: newRoleName,
            oldRole: this.data.currentRole
          });

          // 更新tabBar
          app.updateTabBarByRole();

          wx.showToast({
            title: '角色切换成功',
            icon: 'success'
          });
        }
      } catch (error) {
        console.error('角色切换失败:', error);
        wx.showToast({
          title: error.message || '角色切换失败',
          icon: 'error'
        });
        this.setData({ loading: false });
      }
    },

    // 获取角色名称
    getRoleName(role) {
      const roleNames = {
        'user': '普通用户',
        'pi': 'PI负责人',
        'engineer': '维修工程师',
        'admin': '管理员',
        'super_admin': '超级管理员'
      };
      return roleNames[role] || '未知角色';
    },

    // 获取角色图标
    getRoleIcon(role) {
      const icons = {
        'user': '/images/role-user.png',
        'pi': '/images/role-pi.png',
        'engineer': '/images/role-engineer.png',
        'admin': '/images/role-admin.png',
        'super_admin': '/images/role-super-admin.png'
      };
      return icons[role] || '/images/role-default.png';
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initRoleInfo();
    }
  },

  /**
   * 页面生命周期
   */
  pageLifetimes: {
    show() {
      this.initRoleInfo();
    }
  }
});
