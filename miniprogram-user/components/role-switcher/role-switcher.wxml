<!--components/role-switcher/role-switcher.wxml-->
<view class="role-switcher {{mode}} {{customClass}}">
  <!-- 紧凑模式 -->
  <view wx:if="{{mode === 'compact'}}" class="role-switcher-compact" bindtap="showRoleSelector">
    <view class="current-role">
      <text class="role-name">{{currentRoleName}}</text>
      <view class="switch-icon">
        <text class="icon">⌄</text>
      </view>
    </view>
  </view>

  <!-- 完整模式 -->
  <view wx:else class="role-switcher-full">
    <view class="role-header">
      <text class="role-label">当前身份</text>
    </view>
    <view class="current-role-full" bindtap="showRoleSelector">
      <image class="role-icon" src="{{currentRoleIcon}}" mode="aspectFit"></image>
      <view class="role-info">
        <text class="role-name">{{currentRoleName}}</text>
        <text class="role-hint">点击切换身份</text>
      </view>
      <view class="switch-icon">
        <text class="icon">›</text>
      </view>
    </view>
  </view>

  <!-- 角色选择弹窗 -->
  <view wx:if="{{showRoleList}}" class="role-selector-mask" bindtap="hideRoleSelector">
    <view class="role-selector" catchtap="">
      <view class="selector-header">
        <text class="selector-title">选择身份</text>
        <view class="close-btn" bindtap="hideRoleSelector">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="role-list">
        <view 
          class="role-option {{item.role === currentRole ? 'current' : ''}}"
          wx:for="{{availableRoles}}"
          wx:key="id"
          data-role="{{item.role}}"
          data-roleid="{{item.id}}"
          bindtap="selectRole"
        >
          <image class="option-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <view class="option-info">
            <text class="option-name">{{item.name}}</text>
            <text wx:if="{{item.department}}" class="option-dept">{{item.department}}</text>
          </view>
          <view wx:if="{{item.role === currentRole}}" class="current-indicator">
            <text class="check-icon">✓</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-spinner"></view>
    <text class="loading-text">切换中...</text>
  </view>
</view>
