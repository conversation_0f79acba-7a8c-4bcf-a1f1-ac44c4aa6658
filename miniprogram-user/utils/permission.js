/**
 * 权限管理工具类
 * 用于前端权限检查和角色管理
 */

class PermissionManager {
  constructor() {
    this.app = getApp();
  }

  /**
   * 检查用户是否有特定权限
   * @param {string} permission 权限键，格式：resource.action
   * @returns {boolean}
   */
  hasPermission(permission) {
    const userInfo = this.app.globalData.userInfo;
    if (!userInfo || !userInfo.currentRole) {
      return false;
    }

    // 超级管理员拥有所有权限
    if (userInfo.currentRole === 'super_admin') {
      return true;
    }

    // 检查系统权限
    const systemPermissions = userInfo.systemPermissions || [];
    const hasSystemPermission = systemPermissions.some(p => 
      p.key === permission || p.key === '*'
    );

    if (hasSystemPermission) {
      return true;
    }

    // 检查自定义权限
    const customPermissions = userInfo.customPermissions || [];
    return customPermissions.includes(permission);
  }

  /**
   * 检查用户是否有某个角色
   * @param {string} role 角色名称
   * @returns {boolean}
   */
  hasRole(role) {
    const userInfo = this.app.globalData.userInfo;
    if (!userInfo || !userInfo.roles) {
      return false;
    }

    return userInfo.roles.some(r => r.role === role);
  }

  /**
   * 检查用户是否有多个角色中的任意一个
   * @param {string[]} roles 角色名称数组
   * @returns {boolean}
   */
  hasAnyRole(roles) {
    return roles.some(role => this.hasRole(role));
  }

  /**
   * 检查用户是否有所有指定角色
   * @param {string[]} roles 角色名称数组
   * @returns {boolean}
   */
  hasAllRoles(roles) {
    return roles.every(role => this.hasRole(role));
  }

  /**
   * 获取当前用户角色
   * @returns {string}
   */
  getCurrentRole() {
    const userInfo = this.app.globalData.userInfo;
    return userInfo ? userInfo.currentRole : 'user';
  }

  /**
   * 获取当前用户角色名称
   * @returns {string}
   */
  getCurrentRoleName() {
    return this.app.globalData.currentRoleName || '普通用户';
  }

  /**
   * 检查是否为多角色用户
   * @returns {boolean}
   */
  isMultiRole() {
    return this.app.globalData.isMultiRole || false;
  }

  /**
   * 获取用户所有角色
   * @returns {Array}
   */
  getUserRoles() {
    const userInfo = this.app.globalData.userInfo;
    return userInfo ? (userInfo.roles || []) : [];
  }

  /**
   * 检查设备权限
   * @param {string} action 操作类型：view, create, update, delete
   * @param {Object} equipment 设备对象（可选）
   * @returns {boolean}
   */
  canAccessEquipment(action, equipment = null) {
    const currentRole = this.getCurrentRole();
    const userInfo = this.app.globalData.userInfo;

    switch (action) {
      case 'view':
        if (this.hasPermission('equipment.view_all')) return true;
        if (this.hasPermission('equipment.view_group') && equipment) {
          // 检查是否为同一课题组
          return equipment.department === userInfo.currentRole?.department;
        }
        if (this.hasPermission('equipment.view_own') && equipment) {
          // 检查是否为设备负责人
          return equipment.owner_id === userInfo.id;
        }
        return false;

      case 'create':
        return this.hasPermission('equipment.create');

      case 'update':
        if (this.hasPermission('equipment.manage_all')) return true;
        if (this.hasPermission('equipment.manage_group') && equipment) {
          return equipment.department === userInfo.currentRole?.department;
        }
        if (this.hasPermission('equipment.update_own') && equipment) {
          return equipment.owner_id === userInfo.id;
        }
        return false;

      case 'delete':
        return this.hasPermission('equipment.delete');

      default:
        return false;
    }
  }

  /**
   * 检查工单权限
   * @param {string} action 操作类型
   * @param {Object} order 工单对象（可选）
   * @returns {boolean}
   */
  canAccessOrder(action, order = null) {
    const currentRole = this.getCurrentRole();
    const userInfo = this.app.globalData.userInfo;

    switch (action) {
      case 'view':
        if (this.hasPermission('order.view_all')) return true;
        if (this.hasPermission('order.view_assigned') && order) {
          return order.assignee_id === userInfo.id;
        }
        if (this.hasPermission('order.view_group') && order) {
          // 检查是否为同一课题组的工单
          return order.reporter?.department === userInfo.currentRole?.department;
        }
        if (this.hasPermission('order.view_own') && order) {
          return order.reporter_id === userInfo.id;
        }
        return false;

      case 'create':
        return this.hasPermission('order.create');

      case 'update':
        if (this.hasPermission('order.manage_all')) return true;
        if (this.hasPermission('order.update_assigned') && order) {
          return order.assignee_id === userInfo.id;
        }
        if (this.hasPermission('order.update_own') && order) {
          return order.reporter_id === userInfo.id;
        }
        return false;

      case 'assign':
        return this.hasPermission('order.assign');

      default:
        return false;
    }
  }

  /**
   * 检查用户管理权限
   * @param {string} action 操作类型
   * @param {Object} targetUser 目标用户（可选）
   * @returns {boolean}
   */
  canManageUser(action, targetUser = null) {
    switch (action) {
      case 'view':
        if (this.hasPermission('user.view_all')) return true;
        if (this.hasPermission('user.view_group') && targetUser) {
          // 检查是否为同一课题组
          const userInfo = this.app.globalData.userInfo;
          return targetUser.department === userInfo.currentRole?.department;
        }
        return false;

      case 'manage_roles':
        return this.hasPermission('user.manage_roles') || 
               this.hasPermission('user.manage_group');

      case 'create':
        return this.hasPermission('user.create');

      case 'update':
        return this.hasPermission('user.update');

      case 'delete':
        return this.hasPermission('user.delete');

      default:
        return false;
    }
  }

  /**
   * 获取角色显示信息
   * @param {string} role 角色类型
   * @returns {Object}
   */
  getRoleInfo(role) {
    const roleMap = {
      'user': {
        name: '普通用户',
        icon: '/images/role-user.png',
        color: '#1890ff',
        description: '查看和管理自己的设备，提交维修工单'
      },
      'pi': {
        name: 'PI负责人',
        icon: '/images/role-pi.png',
        color: '#52c41a',
        description: '管理课题组设备和成员，查看所有相关工单'
      },
      'engineer': {
        name: '工程师',
        icon: '/images/role-engineer.png',
        color: '#fa8c16',
        description: '接收和处理维修工单，更新维修进度'
      },
      'admin': {
        name: '管理员',
        icon: '/images/role-admin.png',
        color: '#722ed1',
        description: '管理平台用户、设备和工单，查看统计数据'
      },
      'super_admin': {
        name: '超级管理员',
        icon: '/images/role-super-admin.png',
        color: '#f5222d',
        description: '拥有系统所有权限，管理平台配置'
      }
    };

    return roleMap[role] || {
      name: role,
      icon: '/images/role-default.png',
      color: '#666666',
      description: '未知角色'
    };
  }
}

// 创建单例实例
const permissionManager = new PermissionManager();

module.exports = permissionManager;
