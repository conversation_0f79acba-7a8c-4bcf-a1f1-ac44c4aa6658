// pages/role-select/role-select.js
const app = getApp();

Page({
  data: {
    roles: [],
    selectedRole: '',
    loading: false
  },

  onLoad() {
    this.loadUserRoles();
  },

  // 加载用户角色
  async loadUserRoles() {
    try {
      app.showLoading('加载中...');
      
      const result = await app.request({
        url: '/roles/my-roles',
        method: 'GET'
      });

      if (result.success) {
        const { roles, currentRole } = result.data;
        
        // 转换角色数据为显示格式
        const roleList = roles.map(role => ({
          id: role.id,
          role: role.role,
          name: role.roleName || role.role_name,
          department: role.department,
          description: this.getRoleDescription(role.role),
          icon: this.getRoleIcon(role.role),
          isActive: role.isActive !== false,
          isDefault: role.isDefault,
          priority: role.priority || 0
        }));

        // 按优先级排序
        roleList.sort((a, b) => {
          if (a.priority !== b.priority) {
            return b.priority - a.priority; // 优先级高的在前
          }
          if (a.isDefault !== b.isDefault) {
            return b.isDefault - a.isDefault; // 默认角色在前
          }
          return 0;
        });

        this.setData({
          roles: roleList,
          selectedRole: currentRole || (roleList.length > 0 ? roleList[0].role : '')
        });
      }
    } catch (error) {
      console.error('加载角色失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      app.hideLoading();
    }
  },

  // 选择角色
  onSelectRole(e) {
    const { role, roleid } = e.currentTarget.dataset;
    this.setData({
      selectedRole: role,
      selectedRoleId: roleid
    });
  },

  // 确认角色选择
  async onConfirmRole() {
    if (!this.data.selectedRole) {
      wx.showToast({
        title: '请选择角色',
        icon: 'none'
      });
      return;
    }

    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 使用角色ID进行切换，如果没有则使用角色类型
      const switchData = this.data.selectedRoleId ?
        { targetRoleId: this.data.selectedRoleId } :
        { targetRole: this.data.selectedRole };

      const result = await app.request({
        url: '/roles/switch',
        method: 'POST',
        data: switchData
      });

      if (result.success) {
        // 更新全局数据
        const { currentRole, roleId, roleName, token } = result.data;
        app.globalData.currentRole = currentRole;
        app.globalData.currentRoleId = roleId;
        app.globalData.currentRoleName = roleName;
        if (token) {
          app.globalData.token = token;
          wx.setStorageSync('token', token);
        }

        // 更新用户信息
        if (app.globalData.userInfo) {
          app.globalData.userInfo.currentRole = currentRole;
          app.globalData.userInfo.currentRoleId = roleId;
          wx.setStorageSync('userInfo', app.globalData.userInfo);
        }

        // 更新tabBar
        app.updateTabBarByRole();

        wx.showToast({
          title: '角色切换成功',
          icon: 'success'
        });

        // 延迟跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1500);
      }
    } catch (error) {
      console.error('角色切换失败:', error);
      wx.showToast({
        title: error.message || '角色切换失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 跳过角色选择
  onSkipSelection() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 获取角色描述
  getRoleDescription(role) {
    const descriptions = {
      'user': '查看和管理自己的设备，提交维修工单',
      'pi': '管理课题组设备和成员，查看所有相关工单',
      'engineer': '接收和处理维修工单，更新维修进度',
      'admin': '管理平台用户、设备和工单，查看统计数据',
      'super_admin': '拥有系统所有权限，管理平台配置'
    };
    return descriptions[role] || '暂无描述';
  },

  // 获取角色图标
  getRoleIcon(role) {
    const icons = {
      'user': '/images/role-user.png',
      'pi': '/images/role-pi.png',
      'engineer': '/images/role-engineer.png',
      'admin': '/images/role-admin.png',
      'super_admin': '/images/role-super-admin.png'
    };
    return icons[role] || '/images/role-default.png';
  }
});
