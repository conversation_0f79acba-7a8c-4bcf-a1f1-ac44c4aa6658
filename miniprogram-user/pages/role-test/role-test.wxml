<!--pages/role-test/role-test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">多角色系统测试</text>
    <button class="refresh-btn" bindtap="refreshTest">刷新</button>
  </view>

  <!-- 用户信息 -->
  <view class="section">
    <view class="section-title">用户信息</view>
    <view class="user-info">
      <view class="info-item">
        <text class="label">用户名：</text>
        <text class="value">{{userInfo.username || '未知'}}</text>
      </view>
      <view class="info-item">
        <text class="label">当前角色：</text>
        <text class="value role">{{currentRole}}</text>
      </view>
      <view class="info-item">
        <text class="label">是否多角色：</text>
        <text class="value">{{roles.length > 1 ? '是' : '否'}}</text>
      </view>
    </view>
  </view>

  <!-- 角色列表 -->
  <view class="section">
    <view class="section-title">
      <text>用户角色 ({{roles.length}})</text>
      <button class="small-btn" bindtap="goToRoleSelect">角色选择</button>
    </view>
    <view class="roles-list">
      <view 
        class="role-item {{item.role === currentRole ? 'current' : ''}}"
        wx:for="{{roles}}"
        wx:key="id"
        data-role="{{item.role}}"
        bindtap="viewRoleDetail"
      >
        <view class="role-info">
          <text class="role-name">{{item.roleName || item.name}}</text>
          <text class="role-dept">{{item.department}}</text>
        </view>
        <view wx:if="{{item.role === currentRole}}" class="current-tag">当前</view>
        <view wx:if="{{item.isDefault}}" class="default-tag">默认</view>
      </view>
    </view>
    <button wx:if="{{roles.length > 1}}" class="test-btn" bindtap="testRoleSwitch">
      测试角色切换
    </button>
  </view>

  <!-- 权限列表 -->
  <view class="section">
    <view class="section-title">系统权限 ({{permissions.length}})</view>
    <view class="permissions-list">
      <view 
        class="permission-item"
        wx:for="{{permissions}}"
        wx:key="key"
        data-permission="{{item.key}}"
        bindtap="testPermission"
      >
        <view class="permission-info">
          <text class="permission-name">{{item.name}}</text>
          <text class="permission-key">{{item.key}}</text>
        </view>
        <text class="permission-resource">{{item.resource}}.{{item.action}}</text>
      </view>
    </view>
  </view>

  <!-- 权限测试结果 -->
  <view class="section">
    <view class="section-title">权限测试结果</view>
    <view class="test-results">
      <view 
        class="test-item {{item.passed ? 'passed' : 'failed'}}"
        wx:for="{{testResults}}"
        wx:key="name"
      >
        <view class="test-info">
          <text class="test-name">{{item.name}}</text>
          <text class="test-result">结果: {{item.result}}</text>
          <text wx:if="{{item.expected !== null}}" class="test-expected">
            期望: {{item.expected}}
          </text>
        </view>
        <text class="test-status">{{item.status}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn primary" bindtap="goHome">返回首页</button>
    <button class="action-btn" bindtap="goToRoleSelect">角色管理</button>
  </view>
</view>
