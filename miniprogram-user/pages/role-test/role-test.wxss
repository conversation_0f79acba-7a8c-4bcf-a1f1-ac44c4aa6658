/* pages/role-test/role-test.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.refresh-btn {
  background-color: #1890ff;
  color: white;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
}

.section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.small-btn {
  background-color: #52c41a;
  color: white;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

/* 用户信息 */
.user-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.value.role {
  color: #1890ff;
  font-weight: bold;
}

/* 角色列表 */
.roles-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.role-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx solid transparent;
}

.role-item.current {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.role-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.role-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.role-dept {
  font-size: 24rpx;
  color: #666;
}

.current-tag {
  background-color: #1890ff;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.default-tag {
  background-color: #52c41a;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.test-btn {
  background-color: #fa8c16;
  color: white;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 28rpx;
}

/* 权限列表 */
.permissions-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
}

.permission-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
  flex: 1;
}

.permission-name {
  font-size: 28rpx;
  color: #333;
}

.permission-key {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}

.permission-resource {
  font-size: 24rpx;
  color: #1890ff;
  font-family: monospace;
}

/* 测试结果 */
.test-results {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx;
  border-radius: 6rpx;
}

.test-item.passed {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
}

.test-item.failed {
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
}

.test-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
  flex: 1;
}

.test-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.test-result {
  font-size: 24rpx;
  color: #666;
}

.test-expected {
  font-size: 24rpx;
  color: #999;
}

.test-status {
  font-size: 32rpx;
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  text-align: center;
}

.action-btn.primary {
  background-color: #1890ff;
  color: white;
}

.action-btn:not(.primary) {
  background-color: #f0f0f0;
  color: #333;
}
