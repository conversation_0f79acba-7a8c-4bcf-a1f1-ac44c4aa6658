// pages/role-test/role-test.js
const app = getApp();
const permission = require('../../utils/permission');

Page({
  data: {
    userInfo: null,
    currentRole: '',
    roles: [],
    permissions: [],
    testResults: []
  },

  onLoad() {
    this.loadUserData();
    this.runPermissionTests();
  },

  onShow() {
    this.loadUserData();
  },

  // 加载用户数据
  loadUserData() {
    const userInfo = app.globalData.userInfo;
    const currentRole = app.globalData.currentRole;
    const roles = userInfo ? userInfo.roles : [];

    this.setData({
      userInfo,
      currentRole,
      roles,
      permissions: userInfo ? userInfo.systemPermissions : []
    });
  },

  // 运行权限测试
  runPermissionTests() {
    const tests = [
      {
        name: '查看设备权限',
        test: () => permission.hasPermission('equipment.view_own'),
        expected: true
      },
      {
        name: '创建设备权限',
        test: () => permission.hasPermission('equipment.create'),
        expected: true
      },
      {
        name: '管理所有设备权限',
        test: () => permission.hasPermission('equipment.manage_all'),
        expected: false
      },
      {
        name: '查看工单权限',
        test: () => permission.hasPermission('order.view_own'),
        expected: true
      },
      {
        name: '创建工单权限',
        test: () => permission.hasPermission('order.create'),
        expected: true
      },
      {
        name: '管理用户权限',
        test: () => permission.hasPermission('user.manage_roles'),
        expected: false
      },
      {
        name: '是否为多角色用户',
        test: () => permission.isMultiRole(),
        expected: null // 根据实际情况
      },
      {
        name: '当前角色检查',
        test: () => permission.getCurrentRole(),
        expected: app.globalData.currentRole
      }
    ];

    const testResults = tests.map(test => {
      const result = test.test();
      const passed = test.expected === null ? true : result === test.expected;
      
      return {
        name: test.name,
        result: result,
        expected: test.expected,
        passed: passed,
        status: passed ? '✅' : '❌'
      };
    });

    this.setData({ testResults });
  },

  // 切换到角色选择页面
  goToRoleSelect() {
    wx.navigateTo({
      url: '/pages/role-select/role-select'
    });
  },

  // 测试角色切换
  async testRoleSwitch() {
    const roles = this.data.roles;
    if (roles.length < 2) {
      wx.showToast({
        title: '当前用户只有一个角色',
        icon: 'none'
      });
      return;
    }

    // 切换到第一个不是当前角色的角色
    const currentRole = this.data.currentRole;
    const targetRole = roles.find(r => r.role !== currentRole);
    
    if (!targetRole) {
      wx.showToast({
        title: '没有可切换的角色',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({ title: '切换中...' });
      
      const result = await app.switchRole(targetRole.role, targetRole.id);
      
      if (result.success) {
        wx.showToast({
          title: '角色切换成功',
          icon: 'success'
        });
        
        // 重新加载数据
        setTimeout(() => {
          this.loadUserData();
          this.runPermissionTests();
        }, 1000);
      }
    } catch (error) {
      console.error('角色切换失败:', error);
      wx.showToast({
        title: '角色切换失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 刷新测试
  refreshTest() {
    this.loadUserData();
    this.runPermissionTests();
    wx.showToast({
      title: '已刷新',
      icon: 'success'
    });
  },

  // 查看角色详情
  viewRoleDetail(e) {
    const { role } = e.currentTarget.dataset;
    const roleInfo = permission.getRoleInfo(role);
    
    wx.showModal({
      title: roleInfo.name,
      content: roleInfo.description,
      showCancel: false
    });
  },

  // 测试权限检查
  testPermission(e) {
    const { permission: permKey } = e.currentTarget.dataset;
    const hasPermission = permission.hasPermission(permKey);
    
    wx.showModal({
      title: '权限检查结果',
      content: `权限 "${permKey}": ${hasPermission ? '有权限' : '无权限'}`,
      showCancel: false
    });
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  }
});
