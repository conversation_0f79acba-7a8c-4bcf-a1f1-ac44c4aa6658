#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 用于在云托管环境中初始化数据库表结构
 */

const { sequelize } = require('../src/config/database');
const fs = require('fs');
const path = require('path');

async function initDatabase() {
  try {
    console.log('🚀 开始初始化数据库...');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 尝试使用Sequelize同步
    try {
      await sequelize.sync({ force: false, alter: true });
      console.log('✅ 数据库表结构同步完成');
    } catch (syncError) {
      console.log('⚠️ Sequelize同步失败，尝试执行SQL脚本...');
      console.log('同步错误:', syncError.message);

      // 如果Sequelize同步失败，执行简化的SQL脚本
      const sqlPath = path.join(__dirname, '../database/init-cloud.sql');
      if (fs.existsSync(sqlPath)) {
        const sql = fs.readFileSync(sqlPath, 'utf8');
        const statements = sql.split(';').filter(stmt => stmt.trim());

        for (const statement of statements) {
          if (statement.trim()) {
            try {
              await sequelize.query(statement);
            } catch (queryError) {
              console.log(`⚠️ SQL语句执行失败: ${queryError.message}`);
              console.log(`语句: ${statement.substring(0, 100)}...`);
            }
          }
        }
        console.log('✅ SQL脚本执行完成');
      }
    }

    // 检查是否需要创建初始数据
    try {
      const { User, UserRole } = require('../src/models');

      const userCount = await User.count();
      console.log(`📊 当前用户数量: ${userCount}`);

      if (userCount === 0) {
        console.log('🔧 创建初始管理员用户...');

        // 创建初始管理员用户
        const adminUser = await User.create({
          openid: 'admin_openid_' + Date.now(),
          username: '系统管理员',
          real_name: '系统管理员',
          role: 'super_admin',
          is_verified: true,
          is_active: true
        });

        // 为管理员创建角色
        await UserRole.create({
          user_id: adminUser.id,
          role: 'super_admin',
          role_name: '超级管理员',
          is_active: true,
          is_default: true,
          granted_at: new Date()
        });

        console.log('✅ 初始管理员用户创建完成');
      }
    } catch (modelError) {
      console.log('⚠️ 模型操作失败:', modelError.message);
    }

    console.log('🎉 数据库初始化完成！');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    console.error('错误详情:', error.message);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
