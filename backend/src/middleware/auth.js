const jwt = require('jsonwebtoken');
const { User, UserRole } = require('../models');

// 云托管认证中间件 - 使用微信注入的用户信息
const authenticateToken = async (req, res, next) => {
  try {
    // 优先使用云托管注入的用户信息
    const openid = req.headers['x-wx-openid'];

    if (openid) {
      // 云托管环境：使用微信注入的openid查找用户
      const user = await User.findOne({ where: { openid } });

      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户不存在，请先登录'
        });
      }

      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          message: '用户已被禁用'
        });
      }

      // 获取用户角色信息
      const userRoles = await UserRole.findAll({
        where: {
          user_id: user.id,
          is_active: true
        }
      });

      const defaultRole = await UserRole.findOne({
        where: {
          user_id: user.id,
          is_default: true,
          is_active: true
        }
      });

      const roles = userRoles.map(ur => ur.role);
      const currentRole = defaultRole ? defaultRole.role : user.role;

      // 添加用户信息到请求对象
      req.user = {
        ...user.toJSON(),
        roles,
        currentRole,
        roleId: defaultRole ? defaultRole.id : null,
        researchGroupId: defaultRole ? defaultRole.research_group_id : null,
        permissions: defaultRole ? (defaultRole.permissions || []) : [],
        // 云托管相关信息
        cloudAuth: {
          openid,
          unionid: req.headers['x-wx-unionid'],
          source: req.headers['x-wx-source']
        }
      };

      return next();
    }

    // 回退到传统JWT认证（用于非云托管环境）
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失'
      });
    }

    // 检查是否是云托管的伪token
    if (token.startsWith('wx_cloud_')) {
      const userId = token.replace('wx_cloud_', '');
      const user = await User.findByPk(userId);

      if (!user || !user.is_active) {
        return res.status(401).json({
          success: false,
          message: '用户不存在或已禁用'
        });
      }

      // 获取用户角色信息
      const userRoles = await UserRole.findAll({
        where: {
          user_id: user.id,
          is_active: true
        }
      });

      const defaultRole = await UserRole.findOne({
        where: {
          user_id: user.id,
          is_default: true,
          is_active: true
        }
      });

      const roles = userRoles.map(ur => ur.role);
      const currentRole = defaultRole ? defaultRole.role : user.role;

      req.user = {
        ...user.toJSON(),
        roles,
        currentRole,
        roleId: defaultRole ? defaultRole.id : null,
        researchGroupId: defaultRole ? defaultRole.research_group_id : null,
        permissions: defaultRole ? (defaultRole.permissions || []) : []
      };

      return next();
    }

    // 传统JWT验证
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.userId);

    if (!user || !user.is_active) {
      return res.status(401).json({
        success: false,
        message: '用户不存在或已禁用'
      });
    }

    req.user = {
      ...user.toJSON(),
      currentRole: decoded.currentRole || user.role,
      roleId: decoded.roleId,
      researchGroupId: decoded.researchGroupId,
      permissions: decoded.permissions || []
    };

    next();

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '访问令牌已过期'
      });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的访问令牌'
      });
    }

    console.error('认证中间件错误:', error);
    res.status(500).json({
      success: false,
      message: '认证失败'
    });
  }
};

// 角色权限检查中间件
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证'
      });
    }

    const userRole = req.user.currentRole || req.user.role;
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 管理员权限检查
const requireAdmin = requireRole(['admin', 'super_admin']);

// 工程师权限检查
const requireEngineer = requireRole(['engineer', 'admin', 'super_admin']);

// 超级管理员权限检查
const requireSuperAdmin = requireRole(['super_admin']);

// 权限检查中间件
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证'
      });
    }

    const userPermissions = req.user.permissions || [];
    const userRole = req.user.currentRole || req.user.role;

    // 超级管理员拥有所有权限
    if (userRole === 'super_admin' || userPermissions.includes('*')) {
      return next();
    }

    // 检查是否有指定权限
    if (!userPermissions.includes(permission)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 检查是否为资源所有者或管理员
const requireOwnerOrAdmin = (getResourceOwnerId) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '未认证'
        });
      }

      const userRole = req.user.role;
      const userId = req.user.id;

      // 管理员可以访问所有资源
      if (['admin', 'super_admin'].includes(userRole)) {
        return next();
      }

      // 获取资源所有者ID
      const resourceOwnerId = await getResourceOwnerId(req);
      
      if (userId === resourceOwnerId) {
        return next();
      }

      return res.status(403).json({
        success: false,
        message: '只能访问自己的资源'
      });
    } catch (error) {
      console.error('权限检查错误:', error);
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      });
    }
  };
};

// 微信小程序登录验证
const wechatAuth = async (req, res, next) => {
  try {
    const { code } = req.body;

    // 检查是否有云托管注入的用户信息
    const hasCloudUserInfo = req.headers['x-wx-openid'];

    console.log('微信认证中间件:', {
      hasCloudUserInfo: !!hasCloudUserInfo,
      hasCode: !!code,
      openid: hasCloudUserInfo ? '***' + req.headers['x-wx-openid'].slice(-4) : 'null',
      source: req.headers['x-wx-source']
    });

    // 如果有云托管用户信息，直接通过（优先使用）
    if (hasCloudUserInfo) {
      req.wechatData = {
        openid: req.headers['x-wx-openid'],
        session_key: req.headers['x-wx-session-key'],
        unionid: req.headers['x-wx-unionid']
      };
      console.log('使用云托管注入的用户信息');
      return next();
    }

    // 如果没有云托管用户信息，检查是否有code
    if (code) {
      req.wechatData = {
        code: code
      };
      console.log('使用传统code方式');
      return next();
    }

    // 都没有的情况下返回错误
    return res.status(400).json({
      success: false,
      message: '微信授权码缺失'
    });

  } catch (error) {
    console.error('微信认证错误:', error);
    res.status(500).json({
      success: false,
      message: '微信认证失败'
    });
  }
};

module.exports = {
  authenticateToken,
  requireRole,
  requireAdmin,
  requireEngineer,
  requireSuperAdmin,
  requireOwnerOrAdmin,
  requirePermission,
  wechatAuth
};
