const { User, UserRole, RolePermission, UserSession, ResearchGroup, Organization } = require('../models');
const jwt = require('jsonwebtoken');

class RoleController {
  // 获取用户角色信息
  async getUserRoles(req, res) {
    try {
      const userId = req.user.id;

      const userRoles = await UserRole.findByUserId(userId);
      const defaultRole = await UserRole.getUserDefaultRole(userId);

      const rolesData = await Promise.all(userRoles.map(async (userRole) => {
        const roleData = {
          role: userRole.role,
          roleName: userRole.role_name,
          department: userRole.department,
          permissions: userRole.permissions || [],
          isActive: userRole.is_active,
          isDefault: userRole.is_default,
          grantedAt: userRole.granted_at
        };

        // 如果是课题组相关角色，获取课题组信息
        if (userRole.research_group_id) {
          const researchGroup = await ResearchGroup.findByPk(userRole.research_group_id, {
            include: [{
              model: Organization,
              as: 'organization',
              attributes: ['id', 'name', 'type']
            }]
          });
          
          if (researchGroup) {
            roleData.researchGroup = {
              id: researchGroup.id,
              name: researchGroup.name,
              organization: researchGroup.organization
            };
          }
        }

        return roleData;
      }));

      res.json({
        success: true,
        data: {
          userId,
          roles: rolesData,
          currentRole: req.user.currentRole || (defaultRole ? defaultRole.role : 'user'),
          defaultRole: defaultRole ? defaultRole.role : 'user',
          isMultiRole: rolesData.length > 1
        }
      });

    } catch (error) {
      console.error('获取用户角色错误:', error);
      res.status(500).json({
        success: false,
        message: '获取用户角色失败'
      });
    }
  }

  // 切换用户角色
  async switchRole(req, res) {
    try {
      const userId = req.user.id;
      const { targetRoleId, targetRole } = req.body;

      if (!targetRoleId && !targetRole) {
        return res.status(400).json({
          success: false,
          message: '目标角色ID或角色类型不能为空'
        });
      }

      let userRole;

      // 根据角色ID或角色类型查找用户角色
      if (targetRoleId) {
        userRole = await UserRole.findOne({
          where: {
            id: targetRoleId,
            user_id: userId,
            is_active: true
          }
        });
      } else {
        userRole = await UserRole.findUserRole(userId, targetRole);
      }

      if (!userRole) {
        return res.status(403).json({
          success: false,
          message: '您没有此角色权限'
        });
      }

      if (!userRole.is_active) {
        return res.status(403).json({
          success: false,
          message: '此角色已被禁用'
        });
      }

      // 检查角色是否过期
      if (userRole.isExpired()) {
        return res.status(403).json({
          success: false,
          message: '此角色已过期'
        });
      }

      // 更新用户的当前角色
      await User.update(
        { current_role_id: userRole.id },
        { where: { id: userId } }
      );

      // 更新角色使用统计
      await UserRole.updateUsageStats(userRole.id);

      // 获取角色权限
      const rolePermissions = await RolePermission.getUserPermissions([userRole.role]);

      // 生成新的token，包含角色上下文
      const tokenPayload = {
        userId,
        currentRole: userRole.role,
        roleId: userRole.id,
        researchGroupId: userRole.research_group_id,
        permissions: rolePermissions
      };

      const token = jwt.sign(
        tokenPayload,
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      res.json({
        success: true,
        message: '角色切换成功',
        data: {
          currentRole: userRole.role,
          roleId: userRole.id,
          roleName: userRole.role_name,
          department: userRole.department,
          permissions: rolePermissions,
          customPermissions: userRole.permissions || [],
          token,
          researchGroupId: userRole.research_group_id,
          roleContext: userRole.role_context || {}
        }
      });

    } catch (error) {
      console.error('切换角色错误:', error);
      res.status(500).json({
        success: false,
        message: '角色切换失败'
      });
    }
  }

  // 为用户添加角色（管理员功能）
  async addUserRole(req, res) {
    try {
      const { userId } = req.params;
      const { role, roleName, department, researchGroupId, permissions } = req.body;

      // 验证目标用户是否存在
      const targetUser = await User.findByPk(userId);
      if (!targetUser) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 检查是否已有此角色
      const existingRole = await UserRole.findUserRole(userId, role);
      if (existingRole) {
        return res.status(409).json({
          success: false,
          message: '用户已拥有此角色'
        });
      }

      // 如果是课题组相关角色，验证课题组是否存在
      if (researchGroupId) {
        const researchGroup = await ResearchGroup.findByPk(researchGroupId);
        if (!researchGroup) {
          return res.status(404).json({
            success: false,
            message: '课题组不存在'
          });
        }
      }

      // 创建用户角色
      const userRole = await UserRole.create({
        user_id: userId,
        role,
        role_name: roleName || this.getRoleDisplayName(role),
        department,
        research_group_id: researchGroupId,
        permissions,
        granted_by: req.user.id,
        granted_at: new Date()
      });

      res.status(201).json({
        success: true,
        message: '角色添加成功',
        data: userRole.toJSON()
      });

    } catch (error) {
      console.error('添加用户角色错误:', error);
      res.status(500).json({
        success: false,
        message: '添加角色失败'
      });
    }
  }

  // 移除用户角色（管理员功能）
  async removeUserRole(req, res) {
    try {
      const { userId, role } = req.params;

      const userRole = await UserRole.findUserRole(userId, role);
      if (!userRole) {
        return res.status(404).json({
          success: false,
          message: '用户角色不存在'
        });
      }

      // 不能移除默认角色
      if (userRole.is_default) {
        return res.status(400).json({
          success: false,
          message: '不能移除默认角色'
        });
      }

      await userRole.update({ is_active: false });

      res.json({
        success: true,
        message: '角色移除成功'
      });

    } catch (error) {
      console.error('移除用户角色错误:', error);
      res.status(500).json({
        success: false,
        message: '移除角色失败'
      });
    }
  }

  // 设置默认角色
  async setDefaultRole(req, res) {
    try {
      const userId = req.user.id;
      const { role } = req.body;

      // 验证用户是否拥有此角色
      const userRole = await UserRole.findUserRole(userId, role);
      if (!userRole) {
        return res.status(404).json({
          success: false,
          message: '用户角色不存在'
        });
      }

      // 清除其他默认角色
      await UserRole.update(
        { is_default: false },
        { where: { user_id: userId } }
      );

      // 设置新的默认角色
      await userRole.update({ is_default: true });

      res.json({
        success: true,
        message: '默认角色设置成功'
      });

    } catch (error) {
      console.error('设置默认角色错误:', error);
      res.status(500).json({
        success: false,
        message: '设置默认角色失败'
      });
    }
  }

  // 获取角色显示名称
  getRoleDisplayName(role) {
    const roleNames = {
      'user': '普通用户',
      'pi': '课题组负责人',
      'engineer': '工程师',
      'admin': '管理员',
      'super_admin': '超级管理员'
    };
    return roleNames[role] || role;
  }

  // 获取角色权限配置
  async getRolePermissions(req, res) {
    try {
      const { role } = req.params;

      const permissions = this.getDefaultPermissions(role);

      res.json({
        success: true,
        data: {
          role,
          permissions
        }
      });

    } catch (error) {
      console.error('获取角色权限错误:', error);
      res.status(500).json({
        success: false,
        message: '获取角色权限失败'
      });
    }
  }

  // 获取默认权限配置
  getDefaultPermissions(role) {
    const permissionMap = {
      'user': [
        'equipment:read',
        'equipment:create',
        'equipment:update_own',
        'order:create',
        'order:read_own',
        'order:update_own'
      ],
      'pi': [
        'equipment:read',
        'equipment:create',
        'equipment:update',
        'equipment:delete',
        'order:read',
        'order:create',
        'order:update',
        'user:read_group'
      ],
      'engineer': [
        'order:read_assigned',
        'order:update_assigned',
        'order:accept',
        'order:complete',
        'part:read',
        'part:use'
      ],
      'admin': [
        'user:read',
        'user:create',
        'user:update',
        'user:delete',
        'equipment:read',
        'equipment:create',
        'equipment:update',
        'equipment:delete',
        'order:read',
        'order:create',
        'order:update',
        'order:delete',
        'order:assign',
        'part:read',
        'part:create',
        'part:update',
        'part:delete'
      ],
      'super_admin': ['*']
    };

    return permissionMap[role] || [];
  }
}

module.exports = new RoleController();
