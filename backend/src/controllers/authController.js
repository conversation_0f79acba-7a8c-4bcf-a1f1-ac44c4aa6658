const { User, UserRole } = require('../models');
const axios = require('axios');

class AuthController {
  // 健康检查（用于测试云托管用户信息注入）
  async healthCheck(req, res) {
    try {
      const cloudUserInfo = this.getCloudUserInfo(req);

      res.json({
        success: true,
        message: '服务正常',
        data: {
          timestamp: new Date().toISOString(),
          cloudUserInfo: {
            hasOpenid: !!cloudUserInfo.openid,
            hasUnionid: !!cloudUserInfo.unionid,
            appid: cloudUserInfo.appid,
            source: cloudUserInfo.source,
            hasSessionKey: !!cloudUserInfo.sessionKey
          },
          headers: {
            'x-wx-openid': req.headers['x-wx-openid'] ? '***' + req.headers['x-wx-openid'].slice(-4) : 'null',
            'x-wx-source': req.headers['x-wx-source'],
            'x-wx-appid': req.headers['x-wx-appid']
          }
        }
      });
    } catch (error) {
      console.error('健康检查错误:', error);
      res.status(500).json({
        success: false,
        message: '服务异常'
      });
    }
  }

  // 微信小程序登录
  async wechatLogin(req, res) {
    try {
      const { code, userInfo } = req.body;

      // 检查是否是云托管环境，优先使用云托管注入的用户信息
      const cloudUserInfo = this.getCloudUserInfo(req);

      let openid, unionid, session_key;

      if (cloudUserInfo.openid) {
        // 使用云托管注入的用户信息
        console.log('使用云托管用户信息:', cloudUserInfo);
        openid = cloudUserInfo.openid;
        unionid = cloudUserInfo.unionid;
        session_key = cloudUserInfo.sessionKey;
      } else if (code) {
        // 传统方式：调用微信API获取openid和session_key
        console.log('使用传统登录方式');
        const wechatResponse = await this.getWechatUserInfo(code);

        if (!wechatResponse.openid) {
          return res.status(400).json({
            success: false,
            message: '微信登录失败'
          });
        }

        openid = wechatResponse.openid;
        session_key = wechatResponse.session_key;
        unionid = wechatResponse.unionid;
      } else {
        return res.status(400).json({
          success: false,
          message: '缺少登录凭证'
        });
      }

      // 查找或创建用户
      let user = await User.findByOpenid(openid);
      
      if (!user) {
        // 创建新用户
        user = await User.create({
          openid,
          unionid,
          username: userInfo?.nickName || `用户${Date.now()}`,
          avatar: userInfo?.avatarUrl,
          role: 'user'
        });

        // 为新用户创建默认角色
        await UserRole.create({
          user_id: user.id,
          role: 'user',
          role_name: '普通用户',
          is_default: true,
          is_active: true,
          granted_at: new Date()
        });
      } else {
        // 更新用户信息
        await user.update({
          username: userInfo?.nickName || user.username,
          avatar: userInfo?.avatarUrl || user.avatar,
          last_login_at: new Date()
        });
      }

      // 获取用户角色信息
      const userRoles = await UserRole.findByUserId(user.id);
      const defaultRole = await UserRole.getUserDefaultRole(user.id);

      const roles = userRoles.map(ur => ur.role);
      const currentRole = defaultRole ? defaultRole.role : 'user';
      const isMultiRole = userRoles.length > 1;
      const needRoleSelection = isMultiRole;

      // 在云托管环境中，不需要生成JWT token
      // 微信已经通过请求头注入了用户身份信息
      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: {
            ...user.toJSON(),
            roles,
            currentRole,
            isMultiRole
          },
          // 云托管环境不需要token，但为了兼容前端，返回用户标识
          token: `wx_cloud_${user.id}`,
          refreshToken: `wx_cloud_refresh_${user.id}`,
          needRoleSelection,
          // 添加云托管相关信息
          cloudAuth: {
            openid: cloudUserInfo.openid,
            source: cloudUserInfo.source
          }
        }
      });

    } catch (error) {
      console.error('微信登录错误:', error);
      res.status(500).json({
        success: false,
        message: '登录失败'
      });
    }
  }

  // 刷新token（云托管环境不需要，但保留接口兼容性）
  async refreshToken(req, res) {
    try {
      // 在云托管环境中，微信会自动处理用户身份验证
      // 这里只需要返回成功响应
      res.json({
        success: true,
        message: '云托管环境无需刷新令牌',
        data: {
          token: 'wx_cloud_token',
          refreshToken: 'wx_cloud_refresh_token'
        }
      });
    } catch (error) {
      console.error('刷新令牌错误:', error);
      res.status(500).json({
        success: false,
        message: '令牌刷新失败'
      });
    }
  }

  // 登出
  async logout(req, res) {
    try {
      // 这里可以实现token黑名单机制
      res.json({
        success: true,
        message: '登出成功'
      });
    } catch (error) {
      console.error('登出错误:', error);
      res.status(500).json({
        success: false,
        message: '登出失败'
      });
    }
  }

  // 实名认证
  async verifyIdentity(req, res) {
    try {
      const { realName, idCard, phone, department, employeeId } = req.body;
      const userId = req.user.id;

      // 验证身份证号格式
      if (!this.validateIdCard(idCard)) {
        return res.status(400).json({
          success: false,
          message: '身份证号格式不正确'
        });
      }

      // 验证手机号格式
      if (!this.validatePhone(phone)) {
        return res.status(400).json({
          success: false,
          message: '手机号格式不正确'
        });
      }

      // 更新用户信息
      await User.update({
        real_name: realName,
        id_card: idCard,
        phone,
        department,
        employee_id: employeeId,
        is_verified: true
      }, {
        where: { id: userId }
      });

      res.json({
        success: true,
        message: '实名认证成功'
      });

    } catch (error) {
      console.error('实名认证错误:', error);
      res.status(500).json({
        success: false,
        message: '实名认证失败'
      });
    }
  }

  // 获取云托管注入的用户信息
  getCloudUserInfo(req) {
    const headers = req.headers;

    // 微信云托管会在请求头中注入以下信息
    const cloudUserInfo = {
      openid: headers['x-wx-openid'],
      unionid: headers['x-wx-unionid'],
      appid: headers['x-wx-appid'],
      sessionKey: headers['x-wx-session-key'],
      // 其他可能的头信息
      source: headers['x-wx-source'], // 来源：wx_mp（小程序）
      version: headers['x-wx-version'], // 小程序版本
      scene: headers['x-wx-scene'] // 场景值
    };

    console.log('云托管用户信息头:', {
      openid: cloudUserInfo.openid ? '***' + cloudUserInfo.openid.slice(-4) : 'null',
      unionid: cloudUserInfo.unionid ? '***' + cloudUserInfo.unionid.slice(-4) : 'null',
      appid: cloudUserInfo.appid,
      source: cloudUserInfo.source,
      hasSessionKey: !!cloudUserInfo.sessionKey
    });

    return cloudUserInfo;
  }

  // 获取微信用户信息（传统方式）
  async getWechatUserInfo(code) {
    try {
      const appId = process.env.WECHAT_APP_ID;
      const appSecret = process.env.WECHAT_APP_SECRET;

      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('获取微信用户信息错误:', error);
      throw error;
    }
  }

  // 云托管环境不需要生成JWT token
  // 保留这些方法用于向后兼容，但实际不使用
  generateToken(payload) {
    return `wx_cloud_${typeof payload === 'number' ? payload : payload.userId}`;
  }

  generateRefreshToken(userId) {
    return `wx_cloud_refresh_${userId}`;
  }

  // 验证身份证号
  validateIdCard(idCard) {
    const pattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return pattern.test(idCard);
  }

  // 验证手机号
  validatePhone(phone) {
    const pattern = /^1[3-9]\d{9}$/;
    return pattern.test(phone);
  }
}

module.exports = new AuthController();
