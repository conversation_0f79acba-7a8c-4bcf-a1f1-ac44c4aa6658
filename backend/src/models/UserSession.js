const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const UserSession = sequelize.define('UserSession', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  current_role_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '当前角色ID'
  },
  session_token: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '会话令牌'
  },
  login_time: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '登录时间'
  },
  last_activity: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '最后活动时间'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: 'IP地址'
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '会话是否活跃'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '会话过期时间'
  }
}, {
  tableName: 'user_sessions',
  comment: '用户会话表',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['current_role_id']
    },
    {
      fields: ['session_token']
    },
    {
      fields: ['is_active']
    }
  ]
});

// 实例方法
UserSession.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  delete values.session_token; // 不返回敏感信息
  return values;
};

// 检查会话是否过期
UserSession.prototype.isExpired = function() {
  if (!this.expires_at) return false;
  return new Date() > this.expires_at;
};

// 更新最后活动时间
UserSession.prototype.updateActivity = function() {
  return this.update({ last_activity: new Date() });
};

// 类方法
UserSession.createSession = async function(userId, roleId, sessionData = {}) {
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + 24); // 24小时过期
  
  return this.create({
    user_id: userId,
    current_role_id: roleId,
    session_token: sessionData.token,
    ip_address: sessionData.ip,
    user_agent: sessionData.userAgent,
    expires_at: expiresAt
  });
};

UserSession.findActiveSession = function(userId, token) {
  return this.findOne({
    where: {
      user_id: userId,
      session_token: token,
      is_active: true
    }
  });
};

UserSession.findUserActiveSessions = function(userId) {
  return this.findAll({
    where: {
      user_id: userId,
      is_active: true
    },
    order: [['last_activity', 'DESC']]
  });
};

// 清理过期会话
UserSession.cleanupExpiredSessions = async function() {
  const now = new Date();
  
  const result = await this.update(
    { is_active: false },
    {
      where: {
        expires_at: {
          [sequelize.Op.lt]: now
        },
        is_active: true
      }
    }
  );
  
  return result[0]; // 返回更新的记录数
};

// 终止用户所有会话
UserSession.terminateUserSessions = function(userId) {
  return this.update(
    { is_active: false },
    {
      where: {
        user_id: userId,
        is_active: true
      }
    }
  );
};

// 切换用户角色
UserSession.switchUserRole = async function(userId, newRoleId, token) {
  const session = await this.findOne({
    where: {
      user_id: userId,
      session_token: token,
      is_active: true
    }
  });
  
  if (!session) {
    throw new Error('会话不存在或已过期');
  }
  
  if (session.isExpired()) {
    await session.update({ is_active: false });
    throw new Error('会话已过期');
  }
  
  await session.update({
    current_role_id: newRoleId,
    last_activity: new Date()
  });
  
  return session;
};

module.exports = UserSession;
