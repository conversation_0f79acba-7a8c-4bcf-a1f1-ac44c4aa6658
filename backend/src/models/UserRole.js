const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserRole = sequelize.define('UserRole', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  role: {
    type: DataTypes.ENUM('user', 'pi', 'engineer', 'admin', 'super_admin'),
    allowNull: false,
    comment: '角色类型'
  },
  role_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '角色显示名称'
  },
  department: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '角色所属部门/课题组'
  },
  research_group_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联的课题组ID（适用于PI和用户角色）'
  },
  permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '角色权限配置'
  },
  role_context: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '角色上下文数据（如管辖范围、特殊配置等）'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '角色优先级（数字越大优先级越高）'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '角色是否激活'
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否为默认角色'
  },
  granted_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '授权人ID'
  },
  granted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '授权时间'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '角色过期时间（可选）'
  },
  last_used_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后使用时间'
  },
  usage_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '使用次数'
  }
}, {
  tableName: 'user_roles',
  comment: '用户角色关联表',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['role']
    },
    {
      fields: ['user_id', 'role', 'department'],
      unique: true
    },
    {
      fields: ['priority']
    },
    {
      fields: ['is_default']
    },
    {
      fields: ['research_group_id']
    }
  ]
});

// 实例方法
UserRole.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return values;
};

// 类方法
UserRole.findByUserId = function(userId) {
  return this.findAll({ 
    where: { 
      user_id: userId,
      is_active: true
    },
    order: [['is_default', 'DESC'], ['created_at', 'ASC']]
  });
};

UserRole.findUserRole = function(userId, role) {
  return this.findOne({ 
    where: { 
      user_id: userId,
      role: role,
      is_active: true
    }
  });
};

UserRole.getUserDefaultRole = function(userId) {
  return this.findOne({ 
    where: { 
      user_id: userId,
      is_default: true,
      is_active: true
    }
  });
};

// 获取用户在指定课题组的角色
UserRole.getUserRoleInGroup = function(userId, researchGroupId) {
  return this.findAll({
    where: {
      user_id: userId,
      research_group_id: researchGroupId,
      is_active: true
    },
    order: [['priority', 'DESC'], ['created_at', 'ASC']]
  });
};

// 获取用户的最高优先级角色
UserRole.getUserHighestPriorityRole = function(userId) {
  return this.findOne({
    where: {
      user_id: userId,
      is_active: true
    },
    order: [['priority', 'DESC'], ['is_default', 'DESC'], ['created_at', 'ASC']]
  });
};

// 设置用户的默认角色
UserRole.setUserDefaultRole = async function(userId, roleId) {
  const transaction = await this.sequelize.transaction();

  try {
    // 清除所有默认角色标记
    await this.update(
      { is_default: false },
      {
        where: { user_id: userId },
        transaction
      }
    );

    // 设置新的默认角色
    await this.update(
      { is_default: true },
      {
        where: {
          id: roleId,
          user_id: userId,
          is_active: true
        },
        transaction
      }
    );

    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

// 更新角色使用统计
UserRole.updateUsageStats = function(roleId) {
  return this.update(
    {
      last_used_at: new Date(),
      usage_count: this.sequelize.literal('usage_count + 1')
    },
    {
      where: { id: roleId }
    }
  );
};

// 检查角色是否过期
UserRole.prototype.isExpired = function() {
  if (!this.expires_at) return false;
  return new Date() > this.expires_at;
};

// 获取角色的有效权限（合并默认权限和自定义权限）
UserRole.prototype.getEffectivePermissions = function() {
  // 这里可以实现权限合并逻辑
  return this.permissions || [];
};

// 获取课题组的所有PI
UserRole.getGroupPIs = function(researchGroupId) {
  return this.findAll({ 
    where: { 
      research_group_id: researchGroupId,
      role: 'pi',
      is_active: true
    },
    include: [{
      model: require('./User'),
      as: 'user',
      attributes: ['id', 'username', 'real_name', 'phone', 'email']
    }]
  });
};

// 检查用户是否有指定角色
UserRole.hasRole = async function(userId, role) {
  const userRole = await this.findOne({
    where: {
      user_id: userId,
      role: role,
      is_active: true
    }
  });
  return !!userRole;
};

// 检查用户是否为课题组PI
UserRole.isGroupPI = async function(userId, researchGroupId) {
  const userRole = await this.findOne({
    where: {
      user_id: userId,
      research_group_id: researchGroupId,
      role: 'pi',
      is_active: true
    }
  });
  return !!userRole;
};

// 获取用户管理的课题组
UserRole.getManagedGroups = function(userId) {
  return this.findAll({
    where: {
      user_id: userId,
      role: 'pi',
      is_active: true
    },
    attributes: ['research_group_id'],
    group: ['research_group_id']
  });
};

module.exports = UserRole;
