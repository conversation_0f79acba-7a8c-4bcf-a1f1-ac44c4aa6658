const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const RolePermission = sequelize.define('RolePermission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  role: {
    type: DataTypes.ENUM('user', 'pi', 'engineer', 'admin', 'super_admin'),
    allowNull: false,
    comment: '角色类型'
  },
  permission_key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '权限键'
  },
  permission_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '权限名称'
  },
  permission_description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '权限描述'
  },
  resource_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '资源类型（equipment, order, user等）'
  },
  action: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '操作类型（create, read, update, delete等）'
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否为角色默认权限'
  }
}, {
  tableName: 'role_permissions',
  comment: '角色权限定义表',
  indexes: [
    {
      fields: ['role']
    },
    {
      fields: ['permission_key']
    },
    {
      fields: ['role', 'permission_key'],
      unique: true
    },
    {
      fields: ['resource_type']
    }
  ]
});

// 实例方法
RolePermission.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return values;
};

// 类方法
RolePermission.getPermissionsByRole = function(role) {
  return this.findAll({ 
    where: { 
      role: role,
      is_default: true
    },
    order: [['resource_type', 'ASC'], ['action', 'ASC']]
  });
};

RolePermission.checkPermission = function(role, permissionKey) {
  return this.findOne({ 
    where: { 
      role: role,
      permission_key: permissionKey,
      is_default: true
    }
  });
};

RolePermission.getPermissionsByResource = function(role, resourceType) {
  return this.findAll({ 
    where: { 
      role: role,
      resource_type: resourceType,
      is_default: true
    }
  });
};

// 获取角色的所有权限键
RolePermission.getRolePermissionKeys = async function(role) {
  const permissions = await this.findAll({
    where: { 
      role: role,
      is_default: true
    },
    attributes: ['permission_key']
  });
  
  return permissions.map(p => p.permission_key);
};

// 检查用户是否有特定权限
RolePermission.hasPermission = async function(userRoles, permissionKey) {
  if (!userRoles || userRoles.length === 0) return false;
  
  // 超级管理员拥有所有权限
  if (userRoles.includes('super_admin')) return true;
  
  const permission = await this.findOne({
    where: {
      role: userRoles,
      permission_key: permissionKey,
      is_default: true
    }
  });
  
  return !!permission;
};

// 获取用户的所有权限
RolePermission.getUserPermissions = async function(userRoles) {
  if (!userRoles || userRoles.length === 0) return [];
  
  // 超级管理员拥有所有权限
  if (userRoles.includes('super_admin')) {
    return ['system.full_access'];
  }
  
  const permissions = await this.findAll({
    where: {
      role: userRoles,
      is_default: true
    },
    attributes: ['permission_key', 'permission_name', 'resource_type', 'action']
  });
  
  return permissions.map(p => ({
    key: p.permission_key,
    name: p.permission_name,
    resource: p.resource_type,
    action: p.action
  }));
};

module.exports = RolePermission;
