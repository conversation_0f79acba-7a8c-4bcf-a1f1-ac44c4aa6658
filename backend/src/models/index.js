const { sequelize } = require('../config/database');

// 导入所有模型
const User = require('./User');
const UserRole = require('./UserRole');
const RolePermission = require('./RolePermission');
const UserSession = require('./UserSession');
const Organization = require('./Organization');
const ResearchGroup = require('./ResearchGroup');
const Equipment = require('./Equipment');
const Order = require('./Order');
const Chat = require('./Chat');
const Part = require('./Part');
const PartUsage = require('./PartUsage');
const OrderLog = require('./OrderLog');
const Notification = require('./Notification');

// 定义关联关系

// 用户与用户角色的关系
User.hasMany(UserRole, {
  foreignKey: 'user_id',
  as: 'userRoles'
});
UserRole.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 用户与会话的关系
User.hasMany(UserSession, {
  foreignKey: 'user_id',
  as: 'sessions'
});
UserSession.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 用户角色与会话的关系
UserRole.hasMany(UserSession, {
  foreignKey: 'current_role_id',
  as: 'sessions'
});
UserSession.belongsTo(UserRole, {
  foreignKey: 'current_role_id',
  as: 'currentRole'
});

// 用户与当前角色的关系
User.belongsTo(UserRole, {
  foreignKey: 'current_role_id',
  as: 'currentRole'
});

// 组织与课题组的关系
Organization.hasMany(ResearchGroup, {
  foreignKey: 'organization_id',
  as: 'researchGroups'
});
ResearchGroup.belongsTo(Organization, {
  foreignKey: 'organization_id',
  as: 'organization'
});

// 课题组与用户角色的关系
ResearchGroup.hasMany(UserRole, {
  foreignKey: 'research_group_id',
  as: 'userRoles'
});
UserRole.belongsTo(ResearchGroup, {
  foreignKey: 'research_group_id',
  as: 'researchGroup'
});

// 课题组与设备的关系
ResearchGroup.hasMany(Equipment, {
  foreignKey: 'research_group_id',
  as: 'equipment'
});
Equipment.belongsTo(ResearchGroup, {
  foreignKey: 'research_group_id',
  as: 'researchGroup'
});

// 用户与设备的关系（设备负责人）
User.hasMany(Equipment, {
  foreignKey: 'owner_id',
  as: 'ownedEquipment'
});
Equipment.belongsTo(User, {
  foreignKey: 'owner_id',
  as: 'owner'
});

// 用户与工单的关系（报修用户）
User.hasMany(Order, { 
  foreignKey: 'user_id', 
  as: 'reportedOrders' 
});
Order.belongsTo(User, { 
  foreignKey: 'user_id', 
  as: 'reporter' 
});

// 工程师与工单的关系
User.hasMany(Order, { 
  foreignKey: 'engineer_id', 
  as: 'assignedOrders' 
});
Order.belongsTo(User, { 
  foreignKey: 'engineer_id', 
  as: 'engineer' 
});

// 设备与工单的关系
Equipment.hasMany(Order, { 
  foreignKey: 'equipment_id', 
  as: 'orders' 
});
Order.belongsTo(Equipment, { 
  foreignKey: 'equipment_id', 
  as: 'equipment' 
});

// 工单与聊天记录的关系
Order.hasMany(Chat, { 
  foreignKey: 'order_id', 
  as: 'chats' 
});
Chat.belongsTo(Order, { 
  foreignKey: 'order_id', 
  as: 'order' 
});

// 用户与聊天记录的关系
User.hasMany(Chat, { 
  foreignKey: 'sender_id', 
  as: 'sentMessages' 
});
Chat.belongsTo(User, { 
  foreignKey: 'sender_id', 
  as: 'sender' 
});

User.hasMany(Chat, { 
  foreignKey: 'receiver_id', 
  as: 'receivedMessages' 
});
Chat.belongsTo(User, { 
  foreignKey: 'receiver_id', 
  as: 'receiver' 
});

// 工单与配件使用记录的关系
Order.hasMany(PartUsage, { 
  foreignKey: 'order_id', 
  as: 'partUsages' 
});
PartUsage.belongsTo(Order, { 
  foreignKey: 'order_id', 
  as: 'order' 
});

// 配件与配件使用记录的关系
Part.hasMany(PartUsage, { 
  foreignKey: 'part_id', 
  as: 'usages' 
});
PartUsage.belongsTo(Part, { 
  foreignKey: 'part_id', 
  as: 'part' 
});

// 工单与操作日志的关系
Order.hasMany(OrderLog, { 
  foreignKey: 'order_id', 
  as: 'logs' 
});
OrderLog.belongsTo(Order, { 
  foreignKey: 'order_id', 
  as: 'order' 
});

// 用户与操作日志的关系
User.hasMany(OrderLog, { 
  foreignKey: 'user_id', 
  as: 'operationLogs' 
});
OrderLog.belongsTo(User, { 
  foreignKey: 'user_id', 
  as: 'operator' 
});

// 用户与通知的关系
User.hasMany(Notification, { 
  foreignKey: 'user_id', 
  as: 'notifications' 
});
Notification.belongsTo(User, { 
  foreignKey: 'user_id', 
  as: 'user' 
});

// 创建人和更新人关联（可选）
User.hasMany(User, { 
  foreignKey: 'created_by', 
  as: 'createdUsers' 
});
User.belongsTo(User, { 
  foreignKey: 'created_by', 
  as: 'creator' 
});

Equipment.belongsTo(User, { 
  foreignKey: 'created_by', 
  as: 'creator' 
});

Order.belongsTo(User, { 
  foreignKey: 'created_by', 
  as: 'creator' 
});

module.exports = {
  sequelize,
  User,
  UserRole,
  RolePermission,
  UserSession,
  Organization,
  ResearchGroup,
  Equipment,
  Order,
  Chat,
  Part,
  PartUsage,
  OrderLog,
  Notification
};
