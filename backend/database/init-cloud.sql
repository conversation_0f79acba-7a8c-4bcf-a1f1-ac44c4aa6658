-- 资管维平台数据库初始化脚本
-- 创建数据库
DROP DATABASE ziguan_wei;
CREATE DATABASE IF NOT EXISTS `ziguan_wei` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `ziguan_wei`;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `openid` varchar(100) NOT NULL COMMENT '微信openid',
    `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
    `role` enum('user','engineer','admin','super_admin') DEFAULT 'user' COMMENT '用户角色',
    `department` varchar(100) DEFAULT NULL COMMENT '部门/课题组',
    `employee_id` varchar(50) DEFAULT NULL COMMENT '工号',
    `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
    `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否实名认证',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
    `permissions` json DEFAULT NULL COMMENT '权限配置',
    `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
    `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `openid` (`openid`),
    UNIQUE KEY `unionid` (`unionid`),
    KEY `phone` (`phone`),
    KEY `role` (`role`),
    KEY `department` (`department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 设备表
CREATE TABLE IF NOT EXISTS `equipment` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `equipment_code` varchar(50) NOT NULL COMMENT '设备编号',
    `maintenance_code` varchar(50) NOT NULL COMMENT '维护编号',
    `name` varchar(100) NOT NULL COMMENT '设备名称',
    `brand` varchar(50) DEFAULT NULL COMMENT '品牌',
    `model` varchar(50) DEFAULT NULL COMMENT '型号',
    `serial_number` varchar(100) DEFAULT NULL COMMENT '序列号',
    `category` varchar(50) DEFAULT NULL COMMENT '设备类别',
    `location` varchar(100) DEFAULT NULL COMMENT '设备位置',
    `purchase_date` date DEFAULT NULL COMMENT '采购日期',
    `start_date` date DEFAULT NULL COMMENT '启用日期',
    `warranty_end_date` date DEFAULT NULL COMMENT '保修截止日期',
    `is_under_warranty` tinyint(1) DEFAULT 1 COMMENT '是否在保修期内',
    `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '采购价格',
    `supplier` varchar(100) DEFAULT NULL COMMENT '供应商',
    `supplier_contact` varchar(100) DEFAULT NULL COMMENT '供应商联系方式',
    `images` json DEFAULT NULL COMMENT '设备图片URLs',
    `specifications` text DEFAULT NULL COMMENT '设备规格说明',
    `manual_url` varchar(255) DEFAULT NULL COMMENT '使用手册URL',
    `qr_code` varchar(255) DEFAULT NULL COMMENT '设备二维码URL',
    `status` enum('normal','maintenance','repair','scrapped') DEFAULT 'normal' COMMENT '设备状态',
    `owner_id` int(11) NOT NULL COMMENT '设备负责人ID',
    `department` varchar(100) DEFAULT NULL COMMENT '所属部门/课题组',
    `pi_id` int(11) DEFAULT NULL COMMENT 'PI（课题组负责人）ID',
    `notes` text DEFAULT NULL COMMENT '备注信息',
    `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
    `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `equipment_code` (`equipment_code`),
    UNIQUE KEY `maintenance_code` (`maintenance_code`),
    KEY `owner_id` (`owner_id`),
    KEY `department` (`department`),
    KEY `status` (`status`),
    KEY `brand_model` (`brand`,`model`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `user_roles` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `role` enum('user','pi','engineer','admin','super_admin') NOT NULL COMMENT '角色类型',
    `role_name` varchar(50) NOT NULL COMMENT '角色显示名称',
    `department` varchar(100) DEFAULT NULL COMMENT '角色所属部门/课题组',
    `research_group_id` int(11) DEFAULT NULL COMMENT '关联的课题组ID',
    `permissions` json DEFAULT NULL COMMENT '角色权限配置',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '角色是否激活',
    `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认角色',
    `granted_by` int(11) DEFAULT NULL COMMENT '授权人ID',
    `granted_at` datetime DEFAULT NULL COMMENT '授权时间',
    `expires_at` datetime DEFAULT NULL COMMENT '角色过期时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_role_unique` (`user_id`, `role`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role` (`role`),
    KEY `idx_research_group_id` (`research_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 课题组表
CREATE TABLE IF NOT EXISTS `research_groups` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '课题组名称',
    `code` varchar(50) NOT NULL COMMENT '课题组编码',
    `description` text DEFAULT NULL COMMENT '课题组描述',
    `department` varchar(100) DEFAULT NULL COMMENT '所属部门',
    `pi_id` int(11) DEFAULT NULL COMMENT 'PI负责人ID',
    `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
    `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
    `location` varchar(200) DEFAULT NULL COMMENT '实验室位置',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
    `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `code` (`code`),
    KEY `idx_pi_id` (`pi_id`),
    KEY `idx_department` (`department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课题组表';

-- 工单表
CREATE TABLE IF NOT EXISTS `orders` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_number` varchar(50) NOT NULL COMMENT '工单号',
    `title` varchar(200) NOT NULL COMMENT '工单标题',
    `description` text DEFAULT NULL COMMENT '问题描述',
    `equipment_id` int(11) NOT NULL COMMENT '设备ID',
    `fault_type` enum('hardware','software','maintenance','calibration','other') DEFAULT 'hardware' COMMENT '故障类型',
    `priority` enum('low','medium','high','urgent') DEFAULT 'medium' COMMENT '优先级',
    `status` enum('pending','assigned','in_progress','completed','cancelled') DEFAULT 'pending' COMMENT '工单状态',
    `reporter_id` int(11) NOT NULL COMMENT '报告人ID',
    `assignee_id` int(11) DEFAULT NULL COMMENT '分配的工程师ID',
    `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
    `expected_completion_date` date DEFAULT NULL COMMENT '期望完成日期',
    `actual_completion_date` datetime DEFAULT NULL COMMENT '实际完成时间',
    `images` json DEFAULT NULL COMMENT '问题图片URLs',
    `solution` text DEFAULT NULL COMMENT '解决方案',
    `parts_used` json DEFAULT NULL COMMENT '使用的配件',
    `cost` decimal(10,2) DEFAULT NULL COMMENT '维修成本',
    `rating` int(1) DEFAULT NULL COMMENT '用户评分(1-5)',
    `feedback` text DEFAULT NULL COMMENT '用户反馈',
    `notes` text DEFAULT NULL COMMENT '备注',
    `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
    `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `order_number` (`order_number`),
    KEY `idx_equipment_id` (`equipment_id`),
    KEY `idx_reporter_id` (`reporter_id`),
    KEY `idx_assignee_id` (`assignee_id`),
    KEY `idx_status` (`status`),
    KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工单表';

-- 工单操作日志表
CREATE TABLE IF NOT EXISTS `order_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) NOT NULL COMMENT '工单ID',
    `action` varchar(100) NOT NULL COMMENT '操作类型',
    `description` text DEFAULT NULL COMMENT '操作描述',
    `operator_id` int(11) NOT NULL COMMENT '操作人ID',
    `old_value` json DEFAULT NULL COMMENT '操作前的值',
    `new_value` json DEFAULT NULL COMMENT '操作后的值',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_operator_id` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工单操作日志表';

-- 聊天消息表
CREATE TABLE IF NOT EXISTS `chat_messages` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) NOT NULL COMMENT '关联工单ID',
    `sender_id` int(11) NOT NULL COMMENT '发送者ID',
    `message_type` enum('text','image','file','system') DEFAULT 'text' COMMENT '消息类型',
    `content` text NOT NULL COMMENT '消息内容',
    `file_url` varchar(255) DEFAULT NULL COMMENT '文件URL',
    `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
    `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
    `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
    `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_sender_id` (`sender_id`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 通知表
CREATE TABLE IF NOT EXISTS `notifications` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '接收用户ID',
    `title` varchar(200) NOT NULL COMMENT '通知标题',
    `content` text DEFAULT NULL COMMENT '通知内容',
    `type` enum('system','order','equipment','chat','announcement') DEFAULT 'system' COMMENT '通知类型',
    `related_id` int(11) DEFAULT NULL COMMENT '关联对象ID',
    `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
    `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_type` (`type`),
    KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- 配件表
CREATE TABLE IF NOT EXISTS `parts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '配件名称',
    `part_number` varchar(50) NOT NULL COMMENT '配件编号',
    `category` varchar(50) DEFAULT NULL COMMENT '配件类别',
    `brand` varchar(50) DEFAULT NULL COMMENT '品牌',
    `model` varchar(50) DEFAULT NULL COMMENT '型号',
    `specification` text DEFAULT NULL COMMENT '规格说明',
    `unit` varchar(20) DEFAULT '个' COMMENT '计量单位',
    `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
    `stock_quantity` int(11) DEFAULT 0 COMMENT '库存数量',
    `min_stock` int(11) DEFAULT 0 COMMENT '最低库存预警',
    `supplier` varchar(100) DEFAULT NULL COMMENT '供应商',
    `supplier_contact` varchar(100) DEFAULT NULL COMMENT '供应商联系方式',
    `storage_location` varchar(100) DEFAULT NULL COMMENT '存储位置',
    `notes` text DEFAULT NULL COMMENT '备注',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
    `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `part_number` (`part_number`),
    KEY `idx_category` (`category`),
    KEY `idx_brand_model` (`brand`, `model`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配件表';

-- 配件使用记录表
CREATE TABLE IF NOT EXISTS `part_usages` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) NOT NULL COMMENT '工单ID',
    `part_id` int(11) NOT NULL COMMENT '配件ID',
    `quantity` int(11) NOT NULL COMMENT '使用数量',
    `unit_price` decimal(10,2) DEFAULT NULL COMMENT '使用时单价',
    `total_cost` decimal(10,2) DEFAULT NULL COMMENT '总成本',
    `used_by` int(11) NOT NULL COMMENT '使用人ID',
    `notes` text DEFAULT NULL COMMENT '使用说明',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_part_id` (`part_id`),
    KEY `idx_used_by` (`used_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配件使用记录表';

-- 插入初始管理员用户
INSERT INTO `users` (`openid`, `username`, `real_name`, `role`, `is_verified`, `is_active`) VALUES
                                                                                                ('oyZCT7PHiWm_kUEkWXSRYJgMnn0M', 'admin', '系统管理员', 'super_admin', 1, 1),
                                                                                                ('oyZCT7PHiWm_kUEkWXSRYJgMnn0M', 'engineer1', '工程师1', 'engineer', 1, 1),
                                                                                                ('oyZCT7PHiWm_kUEkWXSRYJgMnn0M', 'user1', '测试用户1', 'user', 1, 1);

-- 插入初始用户角色
INSERT INTO `user_roles` (`user_id`, `role`, `role_name`, `is_active`, `is_default`, `granted_at`) VALUES
                                                                                                        (1, 'super_admin', '超级管理员', 1, 1, NOW()),
                                                                                                        (2, 'engineer', '维修工程师', 1, 1, NOW()),
                                                                                                        (3, 'user', '普通用户', 1, 1, NOW());

-- 插入示例设备数据
INSERT INTO `equipment` (`equipment_code`, `maintenance_code`, `name`, `brand`, `model`, `category`, `owner_id`, `department`, `status`) VALUES
                                                                                                                                             ('EQ20240101001', 'MT20240101001', '高效液相色谱仪', 'Agilent', '1260 Infinity', '分析仪器', 3, '化学系', 'normal'),
                                                                                                                                             ('EQ20240101002', 'MT20240101002', '气相色谱质谱联用仪', 'Thermo', 'TSQ 8000', '分析仪器', 3, '化学系', 'normal'),
                                                                                                                                             ('EQ20240101003', 'MT20240101003', '原子吸收光谱仪', 'PerkinElmer', 'AAnalyst 800', '分析仪器', 3, '化学系', 'normal');

-- 设置字符集
ALTER DATABASE `ziguan_wei` CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
